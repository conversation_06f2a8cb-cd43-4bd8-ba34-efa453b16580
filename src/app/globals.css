@import "tailwindcss";

@theme {
  --font-moderat: Inter, system-ui, sans-serif;
  --color-background: #faf9f7;
  --color-foreground: #1a1a1a;
  --color-muted: #6b6b6b;
  --color-accent: #2d2d2d;
  --color-accent-light: #f5f5f5;
  --color-border: #e5e5e5;
  --color-artistic-red: #c44536;
  --color-artistic-blue: #4a6fa5;
  --color-artistic-yellow: #d4af37;
}

:root {
  /* Artistic color palette inspired by <PERSON> */
  --background: #faf9f7;
  --foreground: #1a1a1a;
  --muted: #6b6b6b;
  --accent: #2d2d2d;
  --accent-light: #f5f5f5;
  --border: #e5e5e5;
  --artistic-red: #c44536;
  --artistic-blue: #4a6fa5;
  --artistic-yellow: #d4af37;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f0f0f;
    --foreground: #e8e8e8;
    --muted: #a0a0a0;
    --accent: #f5f5f5;
    --accent-light: #2a2a2a;
    --border: #2a2a2a;
    --artistic-red: #e55a4a;
    --artistic-blue: #6b8bc7;
    --artistic-yellow: #f4c842;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-moderat), 'Inter', 'Helvetica Neue', sans-serif;
  font-feature-settings: "kern" 1, "liga" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
}

/* Artistic typography styles */
.artistic-heading {
  font-weight: 500;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.artistic-body {
  font-weight: 400;
  letter-spacing: -0.01em;
  line-height: 1.7;
}

.artistic-accent {
  color: var(--artistic-red);
}

/* Experimental layout utilities */
.asymmetric-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  align-items: start;
}

.artistic-border {
  border: 1px solid var(--border);
}

/* Smooth transitions for artistic interactions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Custom cursor only when spatial canvas is active */
.spatial-canvas {
  cursor: none;
}

/* Spatial layout utilities */
.spatial-element {
  position: absolute;
  transform-origin: center;
}

.spatial-element:hover {
  z-index: 10;
}

/* Minimal scrollbar for overlays */
::-webkit-scrollbar {
  width: 2px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--muted);
  border-radius: 1px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Focus styles for accessibility */
button:focus-visible,
[tabindex]:focus-visible {
  outline: 1px solid var(--artistic-red);
  outline-offset: 2px;
}

/* Artistic hover states */
.artistic-hover:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}

/* Text selection styling */
::selection {
  background: var(--artistic-red);
  color: var(--background);
}
