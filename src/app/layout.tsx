import type { <PERSON><PERSON><PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

// Using Inter as a close alternative to Moderat for now
// Replace with actual Moderat font files when available
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-moderat",
  display: "swap",
  weight: ["400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "<PERSON><PERSON> - Visual Artist",
  description: "Tehran-based visual artist and researcher exploring the intersection of design, art, and cultural expression.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="antialiased font-moderat">
        {children}
      </body>
    </html>
  );
}

