"use client";
import { useEffect, useState } from "react";
import dynamic from "next/dynamic";

// Dynamically import SpatialCanvas to avoid SSR issues
const SpatialCanvas = dynamic(() => import("@/components/SpatialCanvas"), {
  ssr: false,
  loading: () => (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-muted animate-pulse">
        Loading...
      </div>
    </div>
  ),
});

export default function Home() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-muted animate-pulse">
          Loading...
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background overflow-hidden">
      <SpatialCanvas />
    </div>
  );
}