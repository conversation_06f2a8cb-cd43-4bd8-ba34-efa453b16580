import { notFound } from "next/navigation";
import works from "@/data/works.json";

export async function generateStaticParams() {
  return works.map((w) => ({ slug: w.slug }));
}

export default function WorkDetail({ params }: { params: { slug: string } }) {
  const work = works.find((w) => w.slug === params.slug);
  if (!work) notFound();

  return (
    <article className="max-w-4xl mx-auto py-20 px-4">
      <h1 className="text-4xl font-bold mb-2">{work.title}</h1>
      <p className="mb-8 text-muted-foreground">
        {work.role} • {work.year}
      </p>
      <p className="text-lg">{work.description}</p>
      {/* Add more blocks here later */}
    </article>
  );
}