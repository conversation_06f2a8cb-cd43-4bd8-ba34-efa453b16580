"use client";
import { motion } from "framer-motion";

export default function About() {
  return (
    <section id="about" className="py-32 px-4 relative overflow-hidden">
      {/* Artistic background elements */}
      <motion.div
        initial={{ opacity: 0, rotate: 0 }}
        whileInView={{ opacity: 0.03, rotate: 45 }}
        transition={{ duration: 3 }}
        viewport={{ once: true }}
        className="absolute top-1/4 right-1/6 w-80 h-80 bg-artistic-blue rounded-full -z-10"
      />
      
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 items-start">
          {/* Main content */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="lg:col-span-8"
          >
            <div className="space-y-12">
              {/* Artistic heading */}
              <div className="space-y-6">
                <motion.h2 
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  viewport={{ once: true }}
                  className="artistic-heading text-4xl md:text-5xl font-medium"
                >
                  About the
                  <span className="block text-artistic-red ml-8">Practice</span>
                </motion.h2>
                
                <motion.div
                  initial={{ width: 0 }}
                  whileInView={{ width: "120px" }}
                  transition={{ duration: 1, delay: 0.6 }}
                  viewport={{ once: true }}
                  className="h-px bg-foreground"
                />
              </div>
              
              {/* Artistic content blocks */}
              <div className="space-y-8">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  viewport={{ once: true }}
                  className="space-y-6"
                >
                  <p className="artistic-body text-xl md:text-2xl leading-relaxed text-muted">
                    My work exists at the intersection of{" "}
                    <span className="artistic-accent">visual research</span>,{" "}
                    <span className="artistic-accent">cultural investigation</span>, and{" "}
                    <span className="artistic-accent">experimental design</span>.
                  </p>
                  
                  <p className="artistic-body text-lg leading-relaxed text-muted">
                    Based in Tehran, I explore how contemporary Persian visual culture 
                    can engage with global design discourse while maintaining its unique 
                    cultural specificity. Each project becomes an opportunity to question 
                    established visual hierarchies and propose new forms of communication.
                  </p>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  viewport={{ once: true }}
                  className="grid grid-cols-1 md:grid-cols-2 gap-8"
                >
                  <div className="space-y-4">
                    <h3 className="artistic-heading text-lg font-medium text-artistic-red">
                      Research Interests
                    </h3>
                    <ul className="space-y-2 text-muted artistic-body">
                      <li>• Contemporary Persian Typography</li>
                      <li>• Cross-Cultural Visual Translation</li>
                      <li>• Editorial as Artistic Medium</li>
                      <li>• Digital Calligraphy Systems</li>
                      <li>• Urban Visual Archaeology</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="artistic-heading text-lg font-medium text-artistic-red">
                      Current Investigations
                    </h3>
                    <ul className="space-y-2 text-muted artistic-body">
                      <li>• Algorithmic Persian Script Generation</li>
                      <li>• Performance Documentation as Design</li>
                      <li>• Cultural Memory in Visual Form</li>
                      <li>• Experimental Publishing Methods</li>
                    </ul>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
          
          {/* Side information */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            viewport={{ once: true }}
            className="lg:col-span-4 space-y-8"
          >
            <div className="space-y-6">
              <div>
                <h4 className="text-sm text-muted uppercase tracking-wider mb-3">
                  Education
                </h4>
                <div className="space-y-2 text-sm">
                  <p className="artistic-body">
                    <span className="text-foreground">MFA Visual Communication</span><br />
                    University of Tehran, 2024
                  </p>
                  <p className="artistic-body">
                    <span className="text-foreground">BA Visual Communication</span><br />
                    University of Tehran, 2019
                  </p>
                </div>
              </div>
              
              <div>
                <h4 className="text-sm text-muted uppercase tracking-wider mb-3">
                  Selected Collaborations
                </h4>
                <div className="space-y-2 text-sm artistic-body">
                  <p>Herfeh:Honarmand Magazine</p>
                  <p>Agar Publication</p>
                  <p>Ettefagh Publishing</p>
                  <p>Tehran Contemporary Art Center</p>
                </div>
              </div>
              
              <div>
                <h4 className="text-sm text-muted uppercase tracking-wider mb-3">
                  Recognition
                </h4>
                <div className="space-y-2 text-sm artistic-body">
                  <p>Tehran Design Week, Featured Artist 2024</p>
                  <p>Persian Typography Award, 2023</p>
                  <p>Cultural Research Grant, 2022</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
