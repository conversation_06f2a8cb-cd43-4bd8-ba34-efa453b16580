"use client";
import { useState } from "react";

export default function Contact() {
  const [sent, setSent] = useState(false);
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const fd = new FormData(e.currentTarget);
    await fetch("/api/contact", {
      method: "POST",
      body: JSON.stringify(Object.fromEntries(fd)),
    });
    setSent(true);
  };

  return (
    <section id="contact" className="py-20 px-4 max-w-xl mx-auto">
      <h2 className="text-4xl font-bold mb-6 text-center">Contact</h2>
      {sent ? (
        <p className="text-center text-green-500">Message sent. Thank you!</p>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-4">
          <input
            name="name"
            placeholder="Name"
            required
            className="w-full p-3 border rounded"
          />
          <input
            name="email"
            type="email"
            placeholder="Email"
            required
            className="w-full p-3 border rounded"
          />
          <textarea
            name="message"
            rows={5}
            placeholder="Message"
            required
            className="w-full p-3 border rounded"
          />
          <button
            type="submit"
            className="w-full bg-accent text-white py-3 rounded hover:scale-105 transition"
          >
            Send
          </button>
        </form>
      )}
    </section>
  );
}
