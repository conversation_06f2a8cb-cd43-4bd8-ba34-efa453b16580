"use client";
import { motion } from "framer-motion";

export default function Hero() {
	return (
		<section className="min-h-screen flex items-center px-4 py-20 relative overflow-hidden">
			{/* Artistic background elements */}
			<motion.div
				initial={{ opacity: 0, scale: 0.8 }}
				animate={{ opacity: 0.03, scale: 1 }}
				transition={{ duration: 2, delay: 0.5 }}
				className="absolute top-1/4 right-1/4 w-96 h-96 rounded-full bg-artistic-red"
			/>
			<motion.div
				initial={{ opacity: 0, x: -100 }}
				animate={{ opacity: 0.05, x: 0 }}
				transition={{ duration: 2, delay: 1 }}
				className="absolute bottom-1/3 left-1/6 w-64 h-64 bg-artistic-blue transform rotate-45"
			/>

			<div className="max-w-7xl mx-auto w-full">
				<div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
					{/* Main content - asymmetric layout */}
					<motion.div
						initial={{ opacity: 0, x: -50 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ duration: 0.8, delay: 0.2 }}
						className="lg:col-span-8 lg:col-start-2"
					>
						<div className="space-y-8">
							{/* Experimental typography */}
							<div className="relative">
								<motion.h1
									initial={{ opacity: 0, y: 30 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.8, delay: 0.4 }}
									className="artistic-heading text-6xl md:text-8xl lg:text-9xl font-medium tracking-tight"
								>
									<span className="block">Mehdi</span>
									<span className="block text-artistic-red ml-8 md:ml-16">Asadi</span>
								</motion.h1>

								{/* Artistic accent line */}
								<motion.div
									initial={{ width: 0 }}
									animate={{ width: "100%" }}
									transition={{ duration: 1, delay: 1.2 }}
									className="h-px bg-foreground mt-4 max-w-xs"
								/>
							</div>

							<motion.div
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ duration: 0.8, delay: 0.8 }}
								className="space-y-4 max-w-2xl"
							>
								<p className="artistic-body text-xl md:text-2xl text-muted leading-relaxed">
									Visual artist & researcher exploring the intersection of
									<span className="artistic-accent"> design, culture,</span> and
									<span className="artistic-accent"> human expression</span> in Tehran.
								</p>

								<p className="artistic-body text-lg text-muted opacity-80">
									Currently investigating new forms of visual communication
									through experimental typography and cross-media artwork.
								</p>
							</motion.div>
						</div>
					</motion.div>

					{/* Minimal navigation hint */}
					<motion.div
						initial={{ opacity: 0, y: 50 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8, delay: 1.4 }}
						className="lg:col-span-3 lg:col-start-10 flex flex-col items-start lg:items-end space-y-6"
					>
						<div className="text-right">
							<p className="text-sm text-muted uppercase tracking-wider mb-2">
								Selected Works
							</p>
							<motion.a
								href="#work"
								whileHover={{ x: 5 }}
								className="group flex items-center space-x-2 text-foreground hover:text-artistic-red transition-colors"
							>
								<span className="text-lg">↓</span>
								<span className="border-b border-transparent group-hover:border-artistic-red transition-colors">
									Explore
								</span>
							</motion.a>
						</div>

						{/* Year indicator */}
						<div className="text-right">
							<p className="text-sm text-muted">
								2024
							</p>
						</div>
					</motion.div>
				</div>
			</div>
		</section>
	);
}
