"use client";
import Link from "next/link";
import ThemeToggle from "./ThemeToggle";

export default function Navbar() {
	return (
		<header className="sticky top-0 z-50 bg-white/80 dark:bg-black/80 backdrop-blur">
			<nav className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
				<Link href="/" className="text-xl font-bold">
					<PERSON><PERSON>
				</Link>
				<div className="flex items-center gap-4">
					<Link
						href="/#work"
						className="hover:text-accent"
					>
						Work
					</Link>
					<Link
						href="/#about"
						className="hover:text-accent"
					>
						About
					</Link>
					<Link
						href="/#contact"
						className="hover:text-accent"
					>
						Contact
					</Link>
					<ThemeToggle />
				</div>
			</nav>
		</header>
	);
}
