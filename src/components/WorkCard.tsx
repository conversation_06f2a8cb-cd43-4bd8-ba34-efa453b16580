"use client";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";

interface WorkCardProps {
  slug: string;
  title: string;
  role: string;
  year: string;
  thumb: string;
  description: string;
  index?: number;
}

export default function WorkCard({
  slug,
  title,
  role,
  year,
  thumb,
  description,
  index = 0,
}: WorkCardProps) {
  // Different layouts for visual variety
  const isLarge = index === 0;
  const isWide = index === 1;

  return (
    <Link href={`/work/${slug}`} scroll={false}>
      <motion.div
        whileHover={{ y: -8 }}
        transition={{ duration: 0.3 }}
        className="group relative overflow-hidden artistic-border bg-background hover:shadow-lg transition-shadow duration-300"
      >
        {/* Image container with artistic aspect ratios */}
        <div className={`
          relative overflow-hidden
          ${isLarge ? 'aspect-[4/5]' : isWide ? 'aspect-[16/9]' : 'aspect-[3/4]'}
        `}>
          <Image
            src={thumb}
            alt={title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-700 ease-out"
          />

          {/* Artistic overlay on hover */}
          <div className="absolute inset-0 bg-artistic-red opacity-0 group-hover:opacity-10 transition-opacity duration-300" />

          {/* Year badge */}
          <div className="absolute top-4 right-4 bg-background/90 backdrop-blur-sm px-2 py-1 text-xs text-muted">
            {year}
          </div>
        </div>

        {/* Content with artistic typography */}
        <div className="p-6 space-y-4">
          <div className="space-y-2">
            <h3 className="artistic-heading text-xl font-medium leading-tight group-hover:text-artistic-red transition-colors">
              {title}
            </h3>

            <p className="text-sm text-muted uppercase tracking-wider">
              {role}
            </p>
          </div>

          <div className="h-px bg-border w-12 group-hover:w-20 group-hover:bg-artistic-red transition-all duration-300" />

          <p className="artistic-body text-sm text-muted leading-relaxed">
            {description}
          </p>

          {/* Subtle interaction hint */}
          <div className="flex items-center space-x-2 text-xs text-muted opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <span>View Project</span>
            <span>→</span>
          </div>
        </div>
      </motion.div>
    </Link>
  );
}
