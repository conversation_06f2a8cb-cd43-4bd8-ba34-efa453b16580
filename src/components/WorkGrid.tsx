"use client";
import { motion } from "framer-motion";
import works from "@/data/artistic-works.json";
import WorkCard from "./WorkCard";

export default function WorkGrid() {
	return (
		<section id="work" className="py-32 px-4 relative">
			{/* Artistic section header */}
			<div className="max-w-7xl mx-auto mb-20">
				<div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						viewport={{ once: true }}
						className="lg:col-span-6"
					>
						<h2 className="artistic-heading text-5xl md:text-6xl font-medium mb-6">
							Selected
							<span className="block text-artistic-red ml-8">Works</span>
						</h2>
						<div className="h-px bg-foreground w-24 mb-6" />
						<p className="artistic-body text-lg text-muted max-w-md">
							A curated collection of visual explorations spanning
							editorial design, cultural research, and experimental typography.
						</p>
					</motion.div>

					<motion.div
						initial={{ opacity: 0, x: 30 }}
						whileInView={{ opacity: 1, x: 0 }}
						transition={{ duration: 0.8, delay: 0.2 }}
						viewport={{ once: true }}
						className="lg:col-span-4 lg:col-start-9 flex items-end"
					>
						<div className="text-right">
							<p className="text-sm text-muted uppercase tracking-wider mb-2">
								2021—2024
							</p>
							<p className="artistic-body text-muted">
								Each project represents a unique investigation into
								visual communication and cultural expression.
							</p>
						</div>
					</motion.div>
				</div>
			</div>

			{/* Asymmetric work grid */}
			<div className="max-w-7xl mx-auto">
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-6 lg:gap-8">
					{works.map((work, index) => (
						<motion.div
							key={work.slug}
							initial={{ opacity: 0, y: 50 }}
							whileInView={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.8, delay: index * 0.1 }}
							viewport={{ once: true }}
							className={`
								${index === 0 ? 'lg:col-span-7' : ''}
								${index === 1 ? 'lg:col-span-5' : ''}
								${index === 2 ? 'lg:col-span-6 lg:col-start-4' : ''}
								${index > 2 ? 'lg:col-span-4' : ''}
							`}
						>
							<WorkCard {...work} index={index} />
						</motion.div>
					))}
				</div>
			</div>

			{/* Artistic background element */}
			<motion.div
				initial={{ opacity: 0, scale: 0.8 }}
				whileInView={{ opacity: 0.02, scale: 1 }}
				transition={{ duration: 2 }}
				viewport={{ once: true }}
				className="absolute top-1/2 left-1/4 w-72 h-72 bg-artistic-yellow rounded-full -z-10"
			/>
		</section>
	);
}
